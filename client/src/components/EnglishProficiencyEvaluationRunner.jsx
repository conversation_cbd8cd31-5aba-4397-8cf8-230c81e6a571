import React, { useState, useEffect, useRef } from 'react';
import { englishProficiencyEvaluationsApi } from '../services/api';

const EnglishProficiencyEvaluationRunner = ({ selectedDatasetId, onRun, onPollingUpdate }) => {
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());
  
  // Model and temperature settings
  const [transcribeModel, setTranscribeModel] = useState('gemini-2.5-flash');
  const [transcribeTemperature, setTranscribeTemperature] = useState(0);
  const [analysisModel, setAnalysisModel] = useState('gemini-2.5-pro');
  const [analysisTemperature, setAnalysisTemperature] = useState(0.4);

  const pollingIntervals = useRef(new Map());

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling this evaluation
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await englishProficiencyEvaluationsApi.getStatus(evaluationId);
        const evaluation = response.data;

        if (evaluation.status === 'completed' || evaluation.status === 'error') {
          // Stop polling for this evaluation
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Notify parent component
          if (onPollingUpdate) {
            onPollingUpdate(evaluation);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling even if there's an error
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!selectedDatasetId) {
      alert('Please select a dataset first');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun(
        selectedDatasetId, 
        transcribeModel, 
        transcribeTemperature, 
        analysisModel, 
        analysisTemperature
      );
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.status === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run English Proficiency evaluation');
    } finally {
      setRunning(false);
    }
  };

  const modelOptions = [
    { value: 'gemini-2.5-pro', label: 'Gemini 2.5 Pro' },
    { value: 'gemini-2.5-flash', label: 'Gemini 2.5 Flash' }
  ];

  const containerStyle = {
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: '24px',
    marginBottom: '24px'
  };

  const sectionStyle = {
    marginBottom: '20px'
  };

  const labelStyle = {
    display: 'block',
    marginBottom: '8px',
    fontWeight: '500',
    color: '#333'
  };

  const selectStyle = {
    width: '100%',
    padding: '8px 12px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px',
    backgroundColor: 'white'
  };

  const sliderContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '12px'
  };

  const sliderStyle = {
    flex: 1,
    height: '6px',
    borderRadius: '3px',
    background: '#ddd',
    outline: 'none',
    cursor: 'pointer'
  };

  const valueDisplayStyle = {
    minWidth: '40px',
    textAlign: 'center',
    fontSize: '14px',
    fontWeight: '500',
    color: '#666'
  };

  return (
    <div style={containerStyle}>
      <h3 style={{ marginTop: 0, marginBottom: '20px', color: '#333' }}>
        🗣️ English Proficiency Evaluation
      </h3>

      <div style={{ marginBottom: '16px' }}>
        <p style={{ margin: '0 0 8px 0', color: '#666' }}>
          This evaluation will analyze English proficiency through a 2-step process:
        </p>
        <ol style={{ margin: '0 0 0 20px', color: '#666' }}>
          <li>Phonetic Transcription - Convert speech to detailed IPA transcription</li>
          <li>English Proficiency Analysis - Evaluate Fluency & Coherence, Vocabulary, Grammar, and Pronunciation</li>
        </ol>
      </div>

      {/* Transcription Settings */}
      <div style={sectionStyle}>
        <h4 style={{ margin: '0 0 12px 0', color: '#555' }}>Transcription Settings</h4>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '12px' }}>
          <div>
            <label style={labelStyle}>Model</label>
            <select
              value={transcribeModel}
              onChange={(e) => setTranscribeModel(e.target.value)}
              style={selectStyle}
            >
              {modelOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label style={labelStyle}>Temperature</label>
            <div style={sliderContainerStyle}>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={transcribeTemperature}
                onChange={(e) => setTranscribeTemperature(parseFloat(e.target.value))}
                style={sliderStyle}
              />
              <span style={valueDisplayStyle}>{transcribeTemperature}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Analysis Settings */}
      <div style={sectionStyle}>
        <h4 style={{ margin: '0 0 12px 0', color: '#555' }}>Analysis Settings</h4>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '12px' }}>
          <div>
            <label style={labelStyle}>Model</label>
            <select
              value={analysisModel}
              onChange={(e) => setAnalysisModel(e.target.value)}
              style={selectStyle}
            >
              {modelOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label style={labelStyle}>Temperature</label>
            <div style={sliderContainerStyle}>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={analysisTemperature}
                onChange={(e) => setAnalysisTemperature(parseFloat(e.target.value))}
                style={sliderStyle}
              />
              <span style={valueDisplayStyle}>{analysisTemperature}</span>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={handleRun}
        disabled={running || !selectedDatasetId}
        style={{
          padding: '10px 20px',
          border: 'none',
          borderRadius: '4px',
          backgroundColor: running || !selectedDatasetId ? '#6c757d' : '#28a745',
          color: 'white',
          cursor: running || !selectedDatasetId ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '16px'
        }}
      >
        {running ? 'Starting Evaluation...' : 'Run English Proficiency Evaluation'}
      </button>

      {pollingEvaluations.size > 0 && (
        <div style={{
          padding: '12px',
          backgroundColor: '#e3f2fd',
          border: '1px solid #90caf9',
          borderRadius: '4px',
          fontSize: '14px',
          color: '#1565c0'
        }}>
          ⏳ {pollingEvaluations.size} evaluation(s) in progress. Results will appear automatically when complete.
        </div>
      )}

      {lastResult && (
        <div style={{
          padding: '12px',
          backgroundColor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          fontSize: '14px'
        }}>
          <strong>Last Run:</strong> {lastResult.status === 'in_progress' ? 'Processing...' : 'Completed'}
          <br />
          <strong>ID:</strong> {lastResult.id}
          <br />
          <strong>Timestamp:</strong> {new Date(lastResult.timestamp).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default EnglishProficiencyEvaluationRunner;
