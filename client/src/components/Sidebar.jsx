import React from 'react';

const Sidebar = ({ activeEvalType, onEvalTypeChange }) => {
  const sidebarStyle = {
    width: '250px',
    backgroundColor: '#f8f9fa',
    borderRight: '1px solid #dee2e6',
    padding: '20px',
    height: '100vh',
    position: 'sticky',
    top: 0
  };

  const menuItemStyle = {
    padding: '12px 16px',
    margin: '8px 0',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontWeight: '500',
    border: 'none',
    width: '100%',
    textAlign: 'left',
    fontSize: '14px'
  };

  const activeMenuItemStyle = {
    ...menuItemStyle,
    backgroundColor: '#007bff',
    color: 'white'
  };

  const inactiveMenuItemStyle = {
    ...menuItemStyle,
    backgroundColor: 'white',
    color: '#495057',
    border: '1px solid #dee2e6'
  };

  return (
    <div style={sidebarStyle}>
      <h3 style={{ 
        marginBottom: '24px', 
        color: '#495057',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        Evaluation Types
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <button
          style={activeEvalType === 'idp' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('idp')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'idp') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'idp') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          📋 IDP Recommendation
        </button>
        
        <button
          style={activeEvalType === 'lgd' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('lgd')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'lgd') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'lgd') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          👥 LGD Analysis
        </button>

        <button
          style={activeEvalType === 'bei' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('bei')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'bei') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'bei') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          🎯 BEI Analysis
        </button>

        <button
          style={activeEvalType === 'etray' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('etray')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'etray') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'etray') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          📧 E-tray Analysis
        </button>

        <button
          style={activeEvalType === 'ai-interview' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('ai-interview')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'ai-interview') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'ai-interview') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          🎤 AI Interview
        </button>

        <button
          style={activeEvalType === 'ai-interview-v2' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('ai-interview-v2')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'ai-interview-v2') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'ai-interview-v2') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          🎤 AI Interview V2
        </button>

        <button
          style={activeEvalType === 'english-proficiency' ? activeMenuItemStyle : inactiveMenuItemStyle}
          onClick={() => onEvalTypeChange('english-proficiency')}
          onMouseEnter={(e) => {
            if (activeEvalType !== 'english-proficiency') {
              e.target.style.backgroundColor = '#e9ecef';
            }
          }}
          onMouseLeave={(e) => {
            if (activeEvalType !== 'english-proficiency') {
              e.target.style.backgroundColor = 'white';
            }
          }}
        >
          🗣️ English Proficiency
        </button>
      </div>

      <div style={{
        marginTop: '32px',
        padding: '16px',
        backgroundColor: '#e3f2fd',
        borderRadius: '6px',
        fontSize: '12px',
        color: '#1565c0'
      }}>
        <strong>Current:</strong><br />
        {activeEvalType === 'idp' ? 'IDP Recommendation Evaluations' :
         activeEvalType === 'lgd' ? 'LGD Analysis Evaluations' :
         activeEvalType === 'bei' ? 'BEI Analysis Evaluations' :
         activeEvalType === 'etray' ? 'E-tray Analysis Evaluations' :
         activeEvalType === 'ai-interview' ? 'AI Interview Evaluations' :
         activeEvalType === 'ai-interview-v2' ? 'AI Interview V2 Evaluations' :
         'English Proficiency Evaluations'}
      </div>
    </div>
  );
};

export default Sidebar;
