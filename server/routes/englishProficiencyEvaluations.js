const express = require('express');
const router = express.Router();
const englishProficiencyGeminiService = require('../services/englishProficiencyGemini');
const supabase = require('../supabaseClient');

// Background processing function
async function processEnglishProficiencyEvaluationAsync(
  evaluationId, 
  dataset, 
  prompt1Content, 
  prompt2Content,
  transcribeModel,
  transcribeTemperature,
  analysisModel,
  analysisTemperature
) {
  try {
    console.log(`Starting background processing for English Proficiency evaluation ${evaluationId}`);

    // Run the English Proficiency prompt chain
    const result = await englishProficiencyGeminiService.runEnglishProficiencyPromptChain(
      dataset, 
      prompt1Content, 
      prompt2Content,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    );

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('english_proficiency_evaluations')
      .update({
        output: result.finalOutput,
        details: result,
        status: 'completed'
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating English Proficiency evaluation:', updateError);
      
      // Update with error status
      await supabase
        .from('english_proficiency_evaluations')
        .update({
          status: 'error',
          details: { error: updateError.message }
        })
        .eq('id', evaluationId);
    } else {
      console.log(`English Proficiency evaluation ${evaluationId} completed successfully`);
    }
  } catch (error) {
    console.error(`Error processing English Proficiency evaluation ${evaluationId}:`, error);
    
    // Update with error status
    await supabase
      .from('english_proficiency_evaluations')
      .update({
        status: 'error',
        details: { error: error.message }
      })
      .eq('id', evaluationId);
  }
}

// GET all English Proficiency evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('english_proficiency_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching English Proficiency evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch English Proficiency evaluations' });
    }

    res.json(data);
  } catch (err) {
    console.error('Error in GET /english-proficiency-evaluations:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET evaluation status by ID
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data, error } = await supabase
      .from('english_proficiency_evaluations')
      .select('id, status, output, details, annotation')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching English Proficiency evaluation status:', error);
      return res.status(500).json({ error: 'Failed to fetch evaluation status' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Evaluation not found' });
    }

    res.json(data);
  } catch (err) {
    console.error('Error in GET /english-proficiency-evaluations/:id/status:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT update annotation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    const { data, error } = await supabase
      .from('english_proficiency_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating English Proficiency evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    res.json(data);
  } catch (err) {
    console.error('Error in PUT /english-proficiency-evaluations/:id/annotation:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST run new English Proficiency evaluation
router.post('/run', async (req, res) => {
  try {
    const { 
      datasetId, 
      transcribeModel = 'gemini-2.5-pro',
      transcribeTemperature = 0.3,
      analysisModel = 'gemini-2.5-pro',
      analysisTemperature = 0.3
    } = req.body;

    if (!datasetId) {
      return res.status(400).json({ error: 'Dataset ID is required' });
    }

    // Validate model options
    const validModels = ['gemini-2.5-pro', 'gemini-2.5-flash'];
    if (!validModels.includes(transcribeModel) || !validModels.includes(analysisModel)) {
      return res.status(400).json({ error: 'Invalid model selection' });
    }

    // Validate temperature ranges
    if (transcribeTemperature < 0 || transcribeTemperature > 1 || 
        analysisTemperature < 0 || analysisTemperature > 1) {
      return res.status(400).json({ error: 'Temperature must be between 0 and 1' });
    }

    // Get dataset
    const { data: dataset, error: datasetError } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', datasetId)
      .single();

    if (datasetError || !dataset) {
      console.error('Error fetching dataset:', datasetError);
      return res.status(404).json({ error: 'Dataset not found' });
    }

    // Validate dataset has question-answer pairs
    if (!dataset.data || !dataset.data.data || !Array.isArray(dataset.data.data) || dataset.data.data.length === 0) {
      return res.status(400).json({ error: 'Dataset must contain question-answer pairs' });
    }

    // Get English Proficiency prompts
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [14, 15]); // English Proficiency prompts

    if (promptsError || !prompts || prompts.length !== 2) {
      console.error('Error fetching English Proficiency prompts:', promptsError);
      return res.status(500).json({ error: 'English Proficiency prompts not found' });
    }

    const prompt1 = prompts.find(p => p.id === 14); // Transcription prompt
    const prompt2 = prompts.find(p => p.id === 15); // Analysis prompt

    if (!prompt1 || !prompt2) {
      return res.status(500).json({ error: 'English Proficiency prompts not found' });
    }

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      dataset_id: datasetId,
      dataset_name: dataset.name,
      output: null,
      status: 'in_progress',
      prompt1Version: prompt1.version,
      prompt2Version: prompt2.version,
      prompt1Content: prompt1.content,
      prompt2Content: prompt2.content,
      timestamp: new Date().toISOString(),
      details: {
        models: {
          transcribe: transcribeModel,
          analysis: analysisModel
        },
        temperatures: {
          transcribe: transcribeTemperature,
          analysis: analysisTemperature
        }
      }
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('english_proficiency_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting English Proficiency evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save English Proficiency evaluation' });
    }

    // Start background processing
    processEnglishProficiencyEvaluationAsync(
      newEvaluation.id, 
      dataset, 
      prompt1.content, 
      prompt2.content,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    );

    res.status(201).json(newEvaluation);
  } catch (err) {
    console.error('Error in POST /english-proficiency-evaluations/run:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
